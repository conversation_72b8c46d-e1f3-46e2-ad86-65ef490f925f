#include <iostream>
#include <unordered_map>
#include <map>
#include "enum.h"
#include "globalFile.h"
using namespace std;

void login(UserType userType) {

}


// 方案1：使用 unordered_map 存储完整的用户信息 - O(1) 查找效率
unordered_map<int, UserTypeInfo> userTypeMap = {
	{USER_STUDENT, {"Student", STUDENT_FILE}},
	{USER_TEACHER, {"Teacher", TEACHER_FILE}},
	{USER_MANAGER, {"Manager", ADMIN_FILE}}
};

int main() {
	int select = 0;

	while (true) {
		system("cls");
		cout << "==========================" << endl;
		// 遍历显示所有用户类型选项
		for (const auto& [key, info] : userTypeMap) {
			cout << key << "." << info.label << endl;
		}
		cout << "0.exit" << endl;
		cout << "==========================" << endl;
		cout << "please input：";
		cin >> select;

		// O(1) 时间复杂度查找
		auto it = userTypeMap.find(select);
		if (it != userTypeMap.end()) {
			cout << "you select " << it->second.label << endl;
			cout << "file path: " << it->second.fileName << endl;
			login(static_cast<UserType>(select));
			system("pause");
		}
		else if (select == 0) {
			exit(0);
		}
		else {
			cout << "input error, please input again!" << endl;
			system("pause");
		}
	}
	return 0;
}